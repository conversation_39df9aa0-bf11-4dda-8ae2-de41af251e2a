import { act, renderHook } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import type { ActionConfig, ExtendedActionConfig } from './useClientActions';
import { useClientAction } from './useClientActions';

// Mock dependencies
vi.mock('../helpers', () => ({
  extractValues: vi.fn((data) => data),
  makeFetchCalls: vi.fn().mockResolvedValue({}),
  renderTemplateStringOnClient: vi.fn((template) => template.template),
  applyTemplateToObject: vi.fn((data) => data),
  evaluateFormConditionExpression: vi.fn(() => true),
}));

vi.mock('../helpers/render-template', () => ({
  renderTemplateObject: vi.fn((data) => data),
}));

vi.mock('../helpers/render-template-functions', () => ({
  templateFunctions: vi.fn(() => ({})),
}));

vi.mock('../../Utilities/checkNetworkOnline', () => ({
  checkIsOnline: vi.fn(() => true),
}));

vi.mock('../useAppStore', () => ({
  useAppStore: {
    getState: vi.fn(() => ({})),
  },
}));

vi.mock('./useModalStore', () => ({
  useModalStore: vi.fn(() => ({
    setModalState: vi.fn(),
  })),
}));

vi.mock('./useAsyncLoaderStore', () => ({
  useAsyncLoaderStore: vi.fn(() => ({
    setAsyncLoading: vi.fn(),
  })),
}));

vi.mock('./useErrorStore', () => ({
  useErrorStore: vi.fn(() => ({
    addError: vi.fn(),
    clearError: vi.fn(),
    clearAllErrors: vi.fn(),
  })),
}));

vi.mock('./useNotesStore', () => ({
  useNotesStore: {
    getState: vi.fn(() => ({
      addNote: vi.fn(),
    })),
  },
}));

vi.mock('./useReminderStore', () => ({
  useReminderStore: {
    getState: vi.fn(() => ({
      addReminder: vi.fn(),
    })),
  },
}));

vi.mock('react-hook-form', () => ({
  useFormContext: vi.fn(() => ({
    getValues: vi.fn(() => ({})),
    resetField: vi.fn(),
    watch: vi.fn(() => ({})),
    formState: {},
  })),
}));

describe('useClientActions - Async Execution Patterns', () => {
  let mockNavigate: ReturnType<typeof vi.fn>;
  let mockLocation: any;
  let executionOrder: string[];
  let executionTimes: Record<string, number>;

  beforeEach(() => {
    mockNavigate = vi.fn();
    mockLocation = { search: '' };
    executionOrder = [];
    executionTimes = {};
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  const createMockAction = (id: string, delay: number = 10): ActionConfig => ({
    type: 'clientAction',
    action: 'log',
    payload: [id],
  });

  const createAsyncMockAction = (
    id: string,
    delay: number = 10
  ): ExtendedActionConfig => ({
    type: 'clientAction',
    action: 'log',
    payload: [id],
    async: true,
  });

  // Helper to track execution order and timing
  const trackExecution = (id: string) => {
    executionOrder.push(id);
    executionTimes[id] = Date.now();
  };

  beforeEach(() => {
    // Mock console.log to track execution
    vi.spyOn(console, 'log').mockImplementation((...args) => {
      if (typeof args[0] === 'string') {
        trackExecution(args[0]);
      }
    });
  });

  describe('callClientActionAsync - Concurrent vs Sequential Behavior', () => {
    it('should execute multiple callClientActionAsync calls concurrently when not awaited', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const startTime = Date.now();

      // Execute without await - should run concurrently
      const promises = [
        result.current.callClientActionAsync(createMockAction('action1', 50)),
        result.current.callClientActionAsync(createMockAction('action2', 30)),
        result.current.callClientActionAsync(createMockAction('action3', 20)),
      ];

      await Promise.all(promises);
      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // Should complete in roughly the time of the longest action (50ms)
      // not the sum of all actions (100ms)
      expect(totalTime).toBeLessThan(80); // Allow some buffer
      expect(executionOrder).toEqual(['action1', 'action2', 'action3']);
    });

    it('should execute multiple callClientActionAsync calls sequentially when awaited', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const startTime = Date.now();

      // Execute with await - should run sequentially
      await result.current.callClientActionAsync(
        createMockAction('action1', 20)
      );
      await result.current.callClientActionAsync(
        createMockAction('action2', 20)
      );
      await result.current.callClientActionAsync(
        createMockAction('action3', 20)
      );

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // Should complete in roughly the sum of all actions
      expect(totalTime).toBeGreaterThan(50); // At least 60ms for 3x20ms actions
      expect(executionOrder).toEqual(['action1', 'action2', 'action3']);

      // Verify sequential execution by checking timestamps
      expect(executionTimes.action2).toBeGreaterThan(executionTimes.action1);
      expect(executionTimes.action3).toBeGreaterThan(executionTimes.action2);
    });
  });

  describe('callClientActionsSequentially', () => {
    it('should execute actions in sequence', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions = [
        createMockAction('seq1', 20),
        createMockAction('seq2', 20),
        createMockAction('seq3', 20),
      ];

      const startTime = Date.now();
      await act(async () => {
        await result.current.callClientActionsSequentially(actions);
      });
      const endTime = Date.now();

      expect(executionOrder).toEqual(['seq1', 'seq2', 'seq3']);
      expect(endTime - startTime).toBeGreaterThan(50); // Sequential timing

      // Verify order by timestamps
      expect(executionTimes.seq2).toBeGreaterThan(executionTimes.seq1);
      expect(executionTimes.seq3).toBeGreaterThan(executionTimes.seq2);
    });

    it('should stop execution on error and propagate the error', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const errorAction: ActionConfig = {
        type: 'clientAction',
        action: 'triggerFetchCall',
        payload: [], // This will cause an error due to missing keycloak
      };

      const actions = [
        createMockAction('before-error'),
        errorAction,
        createMockAction('after-error'), // Should not execute
      ];

      await expect(
        result.current.callClientActionsSequentially(actions)
      ).rejects.toThrow();

      expect(executionOrder).toEqual(['before-error']);
      expect(executionOrder).not.toContain('after-error');
    });
  });

  describe('callClientActionsConcurrently', () => {
    it('should execute all actions concurrently', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions = [
        createMockAction('conc1', 30),
        createMockAction('conc2', 20),
        createMockAction('conc3', 40),
      ];

      const startTime = Date.now();
      await act(async () => {
        await result.current.callClientActionsConcurrently(actions);
      });
      const endTime = Date.now();

      expect(executionOrder).toEqual(['conc1', 'conc2', 'conc3']);
      // Should complete in roughly the time of the longest action (40ms)
      expect(endTime - startTime).toBeLessThan(60);
    });

    it('should fail fast if any action fails', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const errorAction: ActionConfig = {
        type: 'clientAction',
        action: 'triggerFetchCall',
        payload: [],
      };

      const actions = [
        createMockAction('conc1'),
        errorAction,
        createMockAction('conc3'),
      ];

      await expect(
        result.current.callClientActionsConcurrently(actions)
      ).rejects.toThrow();
    });
  });

  describe('callClientActionsWithLimit', () => {
    it('should respect concurrency limit', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions = [
        createMockAction('limit1', 30),
        createMockAction('limit2', 30),
        createMockAction('limit3', 30),
        createMockAction('limit4', 30),
      ];

      const startTime = Date.now();
      await act(async () => {
        await result.current.callClientActionsWithLimit(actions, 2);
      });
      const endTime = Date.now();

      expect(executionOrder).toEqual(['limit1', 'limit2', 'limit3', 'limit4']);

      // With limit of 2, should take roughly 2 batches: 60ms total
      expect(endTime - startTime).toBeGreaterThan(50);
      expect(endTime - startTime).toBeLessThan(80);
    });

    it('should use default concurrency limit of 3', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions = Array.from({ length: 6 }, (_, i) =>
        createMockAction(`default${i + 1}`, 20)
      );

      const startTime = Date.now();
      await act(async () => {
        await result.current.callClientActionsWithLimit(actions);
      });
      const endTime = Date.now();

      // With default limit of 3, should take 2 batches: ~40ms total
      expect(endTime - startTime).toBeGreaterThan(30);
      expect(endTime - startTime).toBeLessThan(60);
    });
  });

  describe('callClientAction with async property', () => {
    it('should return Promise when async: true', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const asyncAction: ExtendedActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['async-test'],
        async: true,
      };

      const promise = result.current.callClientAction(asyncAction);
      expect(promise).toBeInstanceOf(Promise);

      await promise;
      expect(executionOrder).toContain('async-test');
    });

    it('should execute synchronously when async is false or undefined', () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const syncAction: ActionConfig = {
        type: 'clientAction',
        action: 'log',
        payload: ['sync-test'],
      };

      const returnValue = result.current.callClientAction(syncAction);
      expect(returnValue).toBeUndefined();
      expect(executionOrder).toContain('sync-test');
    });
  });

  describe('Real-world scenarios', () => {
    it('should handle form submission workflow correctly', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      // Simulate form submission: validate → submit → navigate
      await act(async () => {
        await result.current.callClientActionsSequentially([
          createMockAction('validate-form'),
          createMockAction('submit-data'),
          createMockAction('navigate-success'),
        ]);
      });

      expect(executionOrder).toEqual([
        'validate-form',
        'submit-data',
        'navigate-success',
      ]);

      // Verify sequential execution
      expect(executionTimes['submit-data']).toBeGreaterThan(
        executionTimes['validate-form']
      );
      expect(executionTimes['navigate-success']).toBeGreaterThan(
        executionTimes['submit-data']
      );
    });

    it('should handle data loading workflow correctly', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      // Simulate concurrent data loading
      const startTime = Date.now();
      await act(async () => {
        await result.current.callClientActionsConcurrently([
          createMockAction('load-user-data', 30),
          createMockAction('load-preferences', 25),
          createMockAction('load-notifications', 20),
        ]);
      });
      const endTime = Date.now();

      expect(executionOrder).toEqual([
        'load-user-data',
        'load-preferences',
        'load-notifications',
      ]);

      // Should complete in roughly the time of the longest action
      expect(endTime - startTime).toBeLessThan(50);
    });

    it('should handle mixed sequential and concurrent workflow', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      await act(async () => {
        // Step 1: Sequential setup
        await result.current.callClientActionsSequentially([
          createMockAction('init-app'),
          createMockAction('setup-auth'),
        ]);

        // Step 2: Concurrent data loading
        await result.current.callClientActionsConcurrently([
          createMockAction('load-data-1'),
          createMockAction('load-data-2'),
        ]);

        // Step 3: Final sequential cleanup
        await result.current.callClientActionAsync(createMockAction('cleanup'));
      });

      expect(executionOrder).toEqual([
        'init-app',
        'setup-auth',
        'load-data-1',
        'load-data-2',
        'cleanup',
      ]);

      // Verify setup is sequential
      expect(executionTimes['setup-auth']).toBeGreaterThan(
        executionTimes['init-app']
      );

      // Verify data loading starts after setup
      expect(executionTimes['load-data-1']).toBeGreaterThan(
        executionTimes['setup-auth']
      );
      expect(executionTimes['load-data-2']).toBeGreaterThan(
        executionTimes['setup-auth']
      );

      // Verify cleanup happens after data loading
      expect(executionTimes['cleanup']).toBeGreaterThan(
        executionTimes['load-data-1']
      );
      expect(executionTimes['cleanup']).toBeGreaterThan(
        executionTimes['load-data-2']
      );
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle Promise.allSettled pattern for partial failures', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const errorAction: ActionConfig = {
        type: 'clientAction',
        action: 'triggerFetchCall',
        payload: [],
      };

      const actions = [
        createMockAction('success1'),
        errorAction,
        createMockAction('success2'),
      ];

      // Use Promise.allSettled to handle partial failures
      const promises = actions.map((action) =>
        result.current
          .callClientActionAsync(action)
          .catch((error) => ({ error }))
      );

      const results = await Promise.allSettled(promises);

      expect(results).toHaveLength(3);
      expect(results[0].status).toBe('fulfilled');
      expect(results[1].status).toBe('fulfilled'); // Caught error
      expect(results[2].status).toBe('fulfilled');

      expect(executionOrder).toContain('success1');
      expect(executionOrder).toContain('success2');
    });

    it('should handle empty action arrays', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      await expect(
        result.current.callClientActionsSequentially([])
      ).resolves.toBeUndefined();

      await expect(
        result.current.callClientActionsConcurrently([])
      ).resolves.toBeUndefined();

      await expect(
        result.current.callClientActionsWithLimit([])
      ).resolves.toBeUndefined();
    });

    it('should handle single action in array functions', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const singleAction = [createMockAction('single')];

      await result.current.callClientActionsSequentially(singleAction);
      await result.current.callClientActionsConcurrently(singleAction);
      await result.current.callClientActionsWithLimit(singleAction);

      expect(
        executionOrder.filter((action) => action === 'single')
      ).toHaveLength(3);
    });

    it('should demonstrate the difference between concurrent and sequential timing', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const actions = [
        createMockAction('timing1', 50),
        createMockAction('timing2', 50),
        createMockAction('timing3', 50),
      ];

      // Test concurrent execution
      const concurrentStart = Date.now();
      await result.current.callClientActionsConcurrently([...actions]);
      const concurrentTime = Date.now() - concurrentStart;

      // Reset execution tracking
      executionOrder.length = 0;

      // Test sequential execution
      const sequentialStart = Date.now();
      await result.current.callClientActionsSequentially([...actions]);
      const sequentialTime = Date.now() - sequentialStart;

      // Concurrent should be much faster than sequential
      expect(concurrentTime).toBeLessThan(sequentialTime);
      expect(concurrentTime).toBeLessThan(80); // ~50ms for concurrent
      expect(sequentialTime).toBeGreaterThan(140); // ~150ms for sequential
    });
  });

  describe('Performance and Memory Tests', () => {
    it('should handle large number of actions efficiently', async () => {
      const { result } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      const largeActionSet = Array.from({ length: 100 }, (_, i) =>
        createMockAction(`bulk${i}`, 1)
      );

      const startTime = Date.now();

      // Test with concurrency limit to prevent overwhelming the system
      await result.current.callClientActionsWithLimit(largeActionSet, 10);

      const endTime = Date.now();

      expect(executionOrder).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(200); // Should complete reasonably fast
    });

    it('should properly clean up resources and not leak memory', async () => {
      const { result, unmount } = renderHook(() =>
        useClientAction({
          navigate: mockNavigate,
          location: mockLocation,
        })
      );

      // Execute some actions
      await result.current.callClientActionAsync(
        createMockAction('cleanup-test')
      );

      // Unmount should not cause any issues
      expect(() => unmount()).not.toThrow();
    });
  });
});
